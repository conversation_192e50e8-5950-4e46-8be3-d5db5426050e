package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
	"time"

	// "context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AssetVehicleRepository struct{}

func NewAssetVehicleRepository() repository.AssetVehicleRepository {
	return &AssetVehicleRepository{}
}

func (r *AssetVehicleRepository) GetAssetVehicles(ctx context.Context, dB database.DBI, assetVechicles *[]models.AssetVehicle, pageSize int, pageNo int, searchKeyword string) (int, error) {
	var totalRecords int64
	query := dB.GetTx().Model(&models.AssetVehicle{})

	if searchKeyword != "" {
		query = query.Joins("INNER JOIN ams_assets ON ams_asset_vehicles.asset_id = ams_assets.id").
			Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+searchKeyword+"%").
			Preload("Asset", func(db *gorm.DB) *gorm.DB {
				return db.Where("LOWER(name) LIKE LOWER(?)", "%"+searchKeyword+"%")
			})
	}

	if err := query.Model(&models.AssetVehicle{}).Count(&totalRecords).Error; err != nil {
		return 0, err
	}

	offset := (pageNo - 1) * pageSize

	query = query.Offset(offset).Limit(pageSize)

	if err := query.Preload("AssetVehicleBodyType").Preload("Asset.Brand").Preload("Asset.AssetStatus").Find(&assetVechicles).Error; err != nil {
		return 0, err
	}

	return int(totalRecords), nil
}

func enrichAssetVehicleQueryWithWhere(query *gorm.DB, where models.AssetVehicleWhere) {
	if where.ClientID != "" {
		query.Where("ams_assets.client_id = ?", where.ClientID)
	}

	if where.AssetID != "" {
		query.Where("ams_asset_vehicles.asset_id = ?", where.AssetID)
	}

	if len(where.AssetIDs) > 0 {
		query.Where("ams_asset_vehicles.asset_id IN ?", where.AssetIDs)
	}

	if len(where.StatusCode) > 0 {
		query.Where("ams_assets.asset_status_code IN ?", where.StatusCode)
	}

	if where.PartnerOwnerID != "" {
		query.Where("ams_assets.partner_owner_id = ?", where.PartnerOwnerID)
	}

	if where.VehicleID != "" {
		query.Where("ams_asset_vehicles.vehicle_id = ?", where.VehicleID)
	}

	if len(where.VehicleIDs) > 0 {
		query.Where("ams_asset_vehicles.vehicle_id IN ?", where.VehicleIDs)
	}

	if len(where.EngineNumbers) > 0 {
		query.Where("ams_asset_vehicles.engine_number IN ?", where.EngineNumbers)
	} // EngineNumbers

	if len(where.RegistrationNumbers) > 0 {
		query.Where("ams_asset_vehicles.registration_number IN ?", where.RegistrationNumbers)
	} // RegistrationNumbers

	if where.ExpiryInMoreThan30Day {
		query.Where("ams_asset_vehicles.inspection_book_expiry_date <= now() + INTERVAL '30 day'").
			Where("ams_asset_vehicles.inspection_book_expiry_date >= now()")
	}

	if len(where.SubCategories) > 0 {
		query.Where("ams_assets.sub_category_code IN ?", where.SubCategories)
	}

	if len(where.Categories) > 0 {
		query.Where("ams_assets.asset_category_code IN ?", where.Categories)
	}

	if len(where.CustomCategories) > 0 {
		query.Where("ams_assets.custom_asset_category_id IN ?", where.CustomCategories)
	}

	if len(where.CustomSubCategories) > 0 {
		query.Where("ams_assets.custom_asset_sub_category_id  IN ?", where.CustomSubCategories)
	}

	if len(where.Locations) > 0 {
		query.Where("ams_locations.name IN ?", where.Locations)
	}

	if len(where.CreateOn) > 0 {
		query.Where("ams_asset_vehicles.created_at::date = Cast(? as Date)", where.CreateOn)
	}

	if where.UpdatedStartDate != "" {
		query.Where("ams_assets.updated_at >= ?", where.UpdatedStartDate)
	} // UpdatedStartDate

	if where.UpdatedEndDate != "" {
		query.Where("ams_assets.updated_at <= ?", where.UpdatedEndDate)
	} // UpdatedEndDate

	if where.UseTyreOptimax {
		query.Where("ams_assets.use_tyre_optimax = ?", true)
	} // UseTyreOptimax

	if where.UseFleetOptimax.Valid {
		query.Where("ams_assets.use_fleet_optimax = ?", where.UseFleetOptimax.Bool)
	} // UseFleetOptimax

	if where.HasAxleConfiguration {
		query.Where("ams_asset_vehicles.axle_configuration != '[]'::jsonb")
	} // HasAxleConfiguration

	if where.HasLinkedAssetTyre {
		query.Where("ams_asset_vehicles.count_linked_tyre_by_trigger > 0")
	} // HasLinkedAssetTyre

	if where.AssignUserID != "" {
		query.Joins(
			`JOIN ams_asset_assignments ON 
			ams_asset_vehicles.asset_id = ams_asset_assignments.asset_id AND user_id = ? 
			AND ams_asset_assignments.unassigned_date_time IS NULL`,
			where.AssignUserID,
		)
	}

	if len(where.Models) > 0 {
		query.Where("ams_assets.model_id IN ?", where.Models)
	}

	if len(where.Brands) > 0 {
		query.Where("ams_assets.brand_id IN ?", where.Brands)
	}

}

func enrichAssetVehicleQueryWithPreload(query *gorm.DB, where models.AssetVehiclePreload) {
	if where.AssetVehicleBodyType {
		query.Preload("AssetVehicleBodyType")
	}

	if where.AssetBrand {
		query.Preload("Asset.Brand")
	}

	if where.AssetAssetStatus {
		query.Preload("Asset.AssetStatus")
	}

	if where.Asset {
		query.Preload("Asset")
	}

	if where.AssetModels {
		query.Preload("Asset.AssetModel")

	}

	if where.Vehicle {
		query.Preload("Vehicle")
	}

	if where.Category {
		query.Preload("Asset.AssetCategory").Preload("Asset.SubCategory")
	}

	if where.CustomAssetCategory {
		query.Preload("Asset.CustomAssetCategory").Preload("Asset.CustomAssetSubCategory")
	}

}

func (r *AssetVehicleRepository) GetAssetVehicleList(ctx context.Context, dB database.DBI, param models.GetAssetVehicleListParam) (int, []models.AssetVehicle, error) {
	var totalRecords int64
	assetVehicles := []models.AssetVehicle{}
	query := dB.GetTx().Model(&models.AssetVehicle{})

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_vehicles.asset_id = ams_assets.id")

	query = query.Joins("LEFT JOIN ams_locations ON ams_assets.location_id = ams_locations.id")

	enrichAssetVehicleQueryWithWhere(query, param.Cond.Where)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.partner_owner_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)

	}
	enrichAssetVehicleQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetVehicles, nil
	}

	query.Order("ams_assets.updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assetVehicles).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetVehicles, nil
}

func (r *AssetVehicleRepository) GetAssetVehiclesWithExpiryVrdIn30Day(ctx context.Context, dB database.DBI) ([]models.AssetVehicle, error) {
	assetVehicles := []models.AssetVehicle{}
	query := dB.GetTx().Model(&assetVehicles)
	query.Where("date_trunc('day', ams_asset_vehicles.vrd_expiry_date) = date_trunc('day', now() + INTERVAL '30 day')")
	query.Preload("Asset")
	err := query.Find(&assetVehicles).Error
	if err != nil {
		return nil, err
	}

	return assetVehicles, nil
}

func (r *AssetVehicleRepository) GetAssetVehiclesWithExpiryInspectionBookIn30Day(ctx context.Context, dB database.DBI) ([]models.AssetVehicle, error) {
	assetVehicles := []models.AssetVehicle{}
	query := dB.GetTx().Model(&assetVehicles)
	query.Where("date_trunc('day', ams_asset_vehicles.inspection_book_expiry_date) = date_trunc('day', now() + INTERVAL '30 day')")
	query.Preload("Asset")
	err := query.Find(&assetVehicles).Error
	if err != nil {
		return nil, err
	}

	return assetVehicles, nil
}

func (r *AssetVehicleRepository) GetAssetVehicleByID(ctx context.Context, dB database.DBI, id string) (*models.AssetVehicle, error) {
	var assetVehicle models.AssetVehicle
	if err := dB.GetTx().Model(&models.AssetVehicle{}).
		Joins("INNER JOIN ams_assets ON ams_asset_vehicles.asset_id = ams_assets.id").
		Where("ams_asset_vehicles.asset_id = ?", id).
		Preload("AssetVehicleBodyType").
		Preload("Asset.Brand").
		Preload("Asset.AssetStatus").
		Preload("Vehicle").
		Preload("Vehicle.Brand").
		First(&assetVehicle).Error; err != nil {
		return nil, err
	}
	return &assetVehicle, nil
}

func (r *AssetVehicleRepository) CreateAssetVehicle(ctx context.Context, dB database.DBI, assetVehicle *models.AssetVehicle) error {
	return dB.GetTx().Create(assetVehicle).Error
}

func (r *AssetVehicleRepository) UpsertAssetVehicle(ctx context.Context, dB database.DBI, assetVehicle *models.AssetVehicle) error {
	return dB.GetTx().Save(assetVehicle).Error
}

func (r *AssetVehicleRepository) IsRegistrationNumberExist(ctx context.Context, dB database.DBI, registrationNumber string, clientID string) (bool, error) {
	var count int64

	if err := dB.GetTx().Model(&models.AssetVehicle{}).
		Where("registration_number = ?", registrationNumber).
		Where("client_id = ?", clientID).
		Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *AssetVehicleRepository) IsEngineNumberExist(ctx context.Context, dB database.DBI, engineNumber string, clientID string) (bool, error) {
	var count int64
	if err := dB.GetTx().
		Model(&models.AssetVehicle{}).
		Where("engine_number = ?", engineNumber).
		Where("client_id = ?", clientID).
		Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *AssetVehicleRepository) UpdateAssetVehicle(ctx context.Context, dB database.DBI, vehicle *models.AssetVehicle) error {
	return dB.GetTx().Updates(vehicle).Error
}

func (r *AssetVehicleRepository) GetAssetVehicle(ctx context.Context, dB database.DBI, condition models.AssetVehicleCondition) (*models.AssetVehicle, error) {
	assetVehicle := models.AssetVehicle{}
	query := dB.GetTx().Model(&assetVehicle)

	query.Joins("INNER JOIN ams_assets ON ams_asset_vehicles.asset_id = ams_assets.id AND ams_assets.deleted_at IS NULL")
	enrichAssetVehicleQueryWithWhere(query, condition.Where)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	if condition.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.First(&assetVehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset vehicle")
		}
		return nil, err
	}

	return &assetVehicle, nil
}

func (r *AssetVehicleRepository) GetAssetVehiclesV2(ctx context.Context, dB database.DBI, condition models.AssetVehicleCondition) ([]models.AssetVehicle, error) {
	assetVehicles := []models.AssetVehicle{}
	query := dB.GetTx().Model(&assetVehicles)

	query.Joins("INNER JOIN ams_assets ON ams_asset_vehicles.asset_id = ams_assets.id AND ams_assets.deleted_at IS NULL")
	enrichAssetVehicleQueryWithWhere(query, condition.Where)
	enrichAssetVehicleQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query = query.Select(condition.Columns)
	}

	if condition.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.Find(&assetVehicles).Error
	if err != nil {
		return nil, err
	}

	return assetVehicles, nil
}

func (r *AssetVehicleRepository) GetVehicles(ctx context.Context, dB database.DBI, param models.GetVehicleListParam) (int, []models.Vehicle, error) {
	var totalRecords int64
	vehicles := []models.Vehicle{}
	query := dB.GetTx().Model(&vehicles)

	if param.SearchKeyword != "" {
		query.Joins("INNER JOIN ams_brands ON ams_vehicles.brand_id = ams_brands.id")

		query.Where(query.Session(&gorm.Session{NewDB: true}).Unscoped().
			Where("LOWER(ams_brands.brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
			Or("LOWER(ams_vehicles.model) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
			Or("LOWER(ams_vehicles.engine_model) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
			Or("LOWER(ams_vehicles.transmission_model) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"))
	}

	enrichVehicleQueryWithWhere(query, param.Cond.Where)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), vehicles, nil
	}

	query.Order("ams_vehicles.updated_at DESC")

	query.Preload("Brand")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&vehicles).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), vehicles, nil
}

func (r *AssetVehicleRepository) CreateVehicle(ctx context.Context, dB database.DBI, vehicle *models.Vehicle) error {
	return dB.GetTx().Create(vehicle).Error
}

func enrichVehicleQueryWithWhere(query *gorm.DB, where models.VehicleWhere) {
	if where.ID != "" {
		query.Where("ams_vehicles.id = ?", where.ID)
	}

	if where.ClientID != "" {
		query.Where("ams_vehicles.client_id = ?", where.ClientID)
	}

	if where.ClientIDAndGeneral != "" {
		query.Where("ams_vehicles.client_id = ? OR ams_vehicles.client_id = 'GENERAL'", where.ClientIDAndGeneral)
	}

	if where.BrandID != "" {
		query.Where("ams_vehicles.brand_id = ?", where.BrandID)
	}

	if len(where.BrandIDs) > 0 {
		query.Where("ams_vehicles.brand_id IN ?", where.BrandIDs)
	}
}

func enrichVehicleQueryWithPreload(query *gorm.DB, preload models.VehiclePreload) {
	if preload.Brand {
		query.Preload("Brand")
	}
}

func (r *AssetVehicleRepository) GetVehicle(ctx context.Context, dB database.DBI, condition models.VehicleCondition) (*models.Vehicle, error) {
	vehicle := &models.Vehicle{}
	query := dB.GetOrm().
		Model(&models.Vehicle{})

	enrichVehicleQueryWithWhere(query, condition.Where)
	enrichVehicleQueryWithPreload(query, condition.Preload)

	err := query.First(vehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("vehicle")
		}

		return nil, err
	}

	return vehicle, nil
}

func (r *AssetVehicleRepository) DeleteVehicleByID(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Delete(&models.Vehicle{}, "id = ?", id).Error
}

func (r *AssetVehicleRepository) UpdateVehicle(ctx context.Context, dB database.DBI, vehicle *models.Vehicle) error {
	return dB.GetTx().
		Model(&models.Vehicle{}).
		Where("id = ?", vehicle.ID).
		Updates(vehicle).Error
}

func (r *AssetVehicleRepository) UpdateAssetVehicleKM(ctx context.Context, dB database.DBI, vehicle *models.AssetVehicle, vehicleKM float64) error {
	model := models.AssetVehicle{}
	return dB.GetTx().Model(&model).Where("asset_id = ?", vehicle.AssetID).Update("vehicle_km", vehicleKM).Error
}

func (r *AssetVehicleRepository) UpdateAssetVehicleKMV2(ctx context.Context, dB database.DBI, assetID string, vehicleKM float64) error {
	return dB.GetTx().
		Where("asset_id = ?", assetID).
		Updates(&models.AssetVehicle{VehicleKM: vehicleKM}).
		Error
}

func (r *AssetVehicleRepository) UpdateAssetVehicleHm(ctx context.Context, dB database.DBI, assetID string, vehicleHm int) error {
	return dB.GetTx().
		Where("asset_id = ?", assetID).
		Updates(&models.AssetVehicle{VehicleHm: vehicleHm}).
		Error
}

func (r *AssetVehicleRepository) GetVehiclesCSV(ctx context.Context, dB database.DBI, cond models.VehicleCondition) ([]models.Vehicle, error) {
	vehicles := []models.Vehicle{}
	query := dB.GetOrm().Model(&vehicles)

	enrichVehicleQueryWithWhere(query, cond.Where)
	query.Or("client_id = ?", "GENERAL")

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Preload("Brand").Find(&vehicles).Error
	if err != nil {
		return nil, err
	}

	return vehicles, nil
}

func (r *AssetVehicleRepository) PopulatePeriodicAssetVehicleStatsHistory(ctx context.Context, dB database.DBI) error {
	return dB.GetTx().Exec(`INSERT INTO
  ams_asset_vehicle_stats_histories (
    datetime,
    created_at,
    asset_id,
    client_id,
    vehicle_km,
    vehicle_hm
  )
SELECT
   (CURRENT_DATE - INTERVAL '1 day') + INTERVAL '23:59:59' datetime,
  now () created_at,
  asset_id,
  client_id,
  CASE
    WHEN use_kilometer THEN vehicle_km
    ELSE NULL
  END vehicle_km,
  CASE
    WHEN use_hourmeter THEN vehicle_hm
    ELSE NULL
  END vehicle_hm
FROM
  ams_asset_vehicles aav
WHERE
  deleted_at IS NULL;`).Error
}

func (r *AssetVehicleRepository) GetAssetVehicleStatsHistory(ctx context.Context, dB database.DBI, datetime time.Time, assetID, clientID string) (*models.AssetVehicleStatsHistory, error) {

	result := &models.AssetVehicleStatsHistory{}
	query := dB.GetTx().
		Model(&models.AssetVehicleStatsHistory{}).
		Where(`DATE("datetime") = DATE(?)`, datetime).
		Where("asset_id = ?", assetID).
		Where("client_id = ?", clientID).
		Order(`"ams_asset_vehicle_stats_histories"."datetime" DESC`)

	err := query.First(result).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset vehicle stats history")
		}
		return nil, err
	}

	return result, nil
}

func (r *AssetVehicleRepository) GetAllActiveAssetIDs(ctx context.Context, dB database.DBI) ([]string, error) {
	result := []string{}
	query := dB.GetTx().Model(&models.AssetVehicle{}).
		Joins("JOIN ams_assets aa ON aa.id = ams_asset_vehicles.asset_id AND aa.deleted_at IS NULL")
		// Joins("JOIN ams_assets aa ON aa.id = ams_asset_vehicles.asset_id AND aa.deleted_at IS NULL AND aa.status_code = ?", constants.ASSET_STATUS_CODE_ACTIVE)

	err := query.Pluck("asset_id", &result).Error
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (r *AssetVehicleRepository) ChartCountTotalVehicles(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	// Filter by client ID and vehicle category
	query.Where("ams_assets.client_id = ?", clientID).
		Where("asset_category_code = ?", constants.ASSET_CATEGORY_VEHICLE_CODE)

	query.Joins("JOIN ams_asset_vehicles ON ams_asset_vehicles.asset_id = ams_assets.id")

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(total),
			Name: "Total Registered Vehicles",
		},
	}, nil
}
